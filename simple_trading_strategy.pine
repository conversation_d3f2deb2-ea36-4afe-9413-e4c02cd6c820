//@version=5
strategy("Simple EMA + Market Structure Strategy", overlay=true, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=2)

// ===== INPUT PARAMETERS =====
ema9_length = input.int(9, "EMA 9 Length", group="Trend Analysis")
ema21_length = input.int(21, "EMA 21 Length", group="Trend Analysis")
swing_length = input.int(5, "Swing Points Length", minval=3, maxval=20, group="Market Structure")
pullback_min = input.float(38.2, "Min Pullback %", minval=20, maxval=50, group="Pullback")
pullback_max = input.float(61.8, "Max Pullback %", minval=50, maxval=80, group="Pullback")
risk_percent = input.float(1.0, "Risk Per Trade %", minval=0.1, maxval=5.0, group="Risk Management")
reward_ratio = input.float(2.0, "Risk:Reward Ratio", minval=1.0, maxval=5.0, group="Risk Management")

// ===== TREND ANALYSIS =====
ema9 = ta.ema(close, ema9_length)
ema21 = ta.ema(close, ema21_length)

// Trend direction
uptrend = ema9 > ema21
downtrend = ema9 < ema21
sideways = not uptrend and not downtrend

// Plot EMAs
plot(ema9, "EMA 9", color=color.blue, linewidth=2)
plot(ema21, "EMA 21", color=color.red, linewidth=2)

// Background color for trend
bgcolor(uptrend ? color.new(color.green, 95) : downtrend ? color.new(color.red, 95) : color.new(color.gray, 95))

// ===== MARKET STRUCTURE ANALYSIS =====
// Swing High and Low detection
swing_high = ta.pivothigh(high, swing_length, swing_length)
swing_low = ta.pivotlow(low, swing_length, swing_length)

// Arrays to store swing points
var array<float> highs = array.new<float>()
var array<float> lows = array.new<float>()
var array<int> high_bars = array.new<int>()
var array<int> low_bars = array.new<int>()

// Store swing points
if not na(swing_high)
    array.push(highs, swing_high)
    array.push(high_bars, bar_index[swing_length])
    if array.size(highs) > 10
        array.shift(highs)
        array.shift(high_bars)

if not na(swing_low)
    array.push(lows, swing_low)
    array.push(low_bars, bar_index[swing_length])
    if array.size(lows) > 10
        array.shift(lows)
        array.shift(low_bars)

// Market Structure Functions
isHigherHigh() =>
    if array.size(highs) >= 2
        latest_high = array.get(highs, array.size(highs) - 1)
        previous_high = array.get(highs, array.size(highs) - 2)
        latest_high > previous_high
    else
        false

isHigherLow() =>
    if array.size(lows) >= 2
        latest_low = array.get(lows, array.size(lows) - 1)
        previous_low = array.get(lows, array.size(lows) - 2)
        latest_low > previous_low
    else
        false

isLowerLow() =>
    if array.size(lows) >= 2
        latest_low = array.get(lows, array.size(lows) - 1)
        previous_low = array.get(lows, array.size(lows) - 2)
        latest_low < previous_low
    else
        false

isLowerHigh() =>
    if array.size(highs) >= 2
        latest_high = array.get(highs, array.size(highs) - 1)
        previous_high = array.get(highs, array.size(highs) - 2)
        latest_high < previous_high
    else
        false

// Market Structure Status
var string market_structure = "NEUTRAL"

// Update market structure only during respective trends
if uptrend and not na(swing_high) and isHigherHigh()
    market_structure := "BULLISH_HH"
    
if uptrend and not na(swing_low) and isHigherLow()
    market_structure := "BULLISH_HL"

if downtrend and not na(swing_low) and isLowerLow()
    market_structure := "BEARISH_LL"
    
if downtrend and not na(swing_high) and isLowerHigh()
    market_structure := "BEARISH_LH"

// Plot swing points with text labels
if not na(swing_high) and uptrend and barstate.isconfirmed
    label.new(bar_index[swing_length], swing_high,
             isHigherHigh() ? "HH" : "H",
             style=label.style_label_down,
             color=isHigherHigh() ? color.green : color.gray,
             textcolor=color.white, size=size.small)

if not na(swing_low) and uptrend and barstate.isconfirmed
    label.new(bar_index[swing_length], swing_low,
             isHigherLow() ? "HL" : "L",
             style=label.style_label_up,
             color=isHigherLow() ? color.green : color.gray,
             textcolor=color.white, size=size.small)

if not na(swing_high) and downtrend and barstate.isconfirmed
    label.new(bar_index[swing_length], swing_high,
             isLowerHigh() ? "LH" : "H",
             style=label.style_label_down,
             color=isLowerHigh() ? color.red : color.gray,
             textcolor=color.white, size=size.small)

if not na(swing_low) and downtrend and barstate.isconfirmed
    label.new(bar_index[swing_length], swing_low,
             isLowerLow() ? "LL" : "L",
             style=label.style_label_up,
             color=isLowerLow() ? color.red : color.gray,
             textcolor=color.white, size=size.small)

// ===== PULLBACK ANALYSIS =====
// Calculate pullback from recent swing
var float pullback_start = na
var float pullback_current = na
var bool valid_pullback = false

// For uptrend: pullback from recent high
if uptrend and not na(swing_high)
    pullback_start := swing_high

if uptrend and not na(pullback_start)
    pullback_current := (pullback_start - close) / pullback_start * 100
    valid_pullback := pullback_current >= pullback_min and pullback_current <= pullback_max

// For downtrend: pullback from recent low  
if downtrend and not na(swing_low)
    pullback_start := swing_low

if downtrend and not na(pullback_start)
    pullback_current := (close - pullback_start) / pullback_start * 100
    valid_pullback := pullback_current >= pullback_min and pullback_current <= pullback_max

// Reset pullback if trend changes
if ta.change(uptrend) or ta.change(downtrend)
    pullback_start := na
    valid_pullback := false

// ===== ENTRY CONDITIONS =====
// Long conditions
long_trend_ok = uptrend
long_structure_ok = market_structure == "BULLISH_HH" or market_structure == "BULLISH_HL"
long_pullback_ok = valid_pullback and close > ema21
long_entry = long_trend_ok and long_structure_ok and long_pullback_ok

// Short conditions  
short_trend_ok = downtrend
short_structure_ok = market_structure == "BEARISH_LL" or market_structure == "BEARISH_LH"
short_pullback_ok = valid_pullback and close < ema21
short_entry = short_trend_ok and short_structure_ok and short_pullback_ok

// ===== RISK MANAGEMENT =====
calculatePositionSize(entryPrice, stopLoss) =>
    riskAmount = strategy.equity * risk_percent / 100
    riskPerShare = math.abs(entryPrice - stopLoss)
    riskAmount / riskPerShare

// ===== TRADE EXECUTION =====
if long_entry and strategy.position_size == 0
    stop_loss = close - ta.atr(14) * 1.5
    take_profit = close + (close - stop_loss) * reward_ratio
    pos_size = calculatePositionSize(close, stop_loss)
    
    strategy.entry("Long", strategy.long, qty=pos_size)
    strategy.exit("Long Exit", from_entry="Long", stop=stop_loss, limit=take_profit)
    
    // Entry label
    if barstate.isconfirmed
        label.new(bar_index, low, 
                 "🟢 LONG\n" + 
                 "Entry: " + str.tostring(close, "#.##") + "\n" + 
                 "SL: " + str.tostring(stop_loss, "#.##") + "\n" + 
                 "TP: " + str.tostring(take_profit, "#.##") + "\n" +
                 "Structure: " + market_structure,
                 style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)

if short_entry and strategy.position_size == 0
    stop_loss = close + ta.atr(14) * 1.5
    take_profit = close - (stop_loss - close) * reward_ratio
    pos_size = calculatePositionSize(close, stop_loss)
    
    strategy.entry("Short", strategy.short, qty=pos_size)
    strategy.exit("Short Exit", from_entry="Short", stop=stop_loss, limit=take_profit)
    
    // Entry label
    if barstate.isconfirmed
        label.new(bar_index, high, 
                 "🔴 SHORT\n" + 
                 "Entry: " + str.tostring(close, "#.##") + "\n" + 
                 "SL: " + str.tostring(stop_loss, "#.##") + "\n" + 
                 "TP: " + str.tostring(take_profit, "#.##") + "\n" +
                 "Structure: " + market_structure,
                 style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)

// ===== INFORMATION DISPLAY =====
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
    
    table.cell(info_table, 0, 0, "📊 MARKET INFO", text_color=color.white, bgcolor=color.blue)
    table.cell(info_table, 1, 0, "", text_color=color.white, bgcolor=color.blue)
    
    table.cell(info_table, 0, 1, "Trend", text_color=color.black, bgcolor=color.gray)
    trend_text = uptrend ? "🟢 UPTREND" : downtrend ? "🔴 DOWNTREND" : "⚪ SIDEWAYS"
    table.cell(info_table, 1, 1, trend_text, text_color=color.black)
    
    table.cell(info_table, 0, 2, "Structure", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 2, market_structure, text_color=color.black)
    
    table.cell(info_table, 0, 3, "Pullback", text_color=color.black, bgcolor=color.gray)
    pullback_text = valid_pullback ? "✅ Valid (" + str.tostring(pullback_current, "#.#") + "%)" : "❌ Invalid"
    table.cell(info_table, 1, 3, pullback_text, text_color=color.black)
    
    table.cell(info_table, 0, 4, "Long Setup", text_color=color.black, bgcolor=color.gray)
    long_status = long_entry ? "🟢 READY" : "⏳ Wait"
    table.cell(info_table, 1, 4, long_status, text_color=color.black)
    
    table.cell(info_table, 0, 5, "Short Setup", text_color=color.black, bgcolor=color.gray)
    short_status = short_entry ? "🔴 READY" : "⏳ Wait"
    table.cell(info_table, 1, 5, short_status, text_color=color.black)

// Plot entry signals
plotshape(long_entry, "Long Signal", shape.triangleup, location.belowbar, color.lime, size=size.normal)
plotshape(short_entry, "Short Signal", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Alerts
if long_entry and barstate.isconfirmed
    alert("🟢 LONG SIGNAL - " + syminfo.ticker + " at " + str.tostring(close), alert.freq_once_per_bar)

if short_entry and barstate.isconfirmed
    alert("🔴 SHORT SIGNAL - " + syminfo.ticker + " at " + str.tostring(close), alert.freq_once_per_bar)
