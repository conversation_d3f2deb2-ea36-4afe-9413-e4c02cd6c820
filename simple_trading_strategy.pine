//@version=5
strategy("Simple EMA + Market Structure Strategy", overlay=true, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=2)

// ===== INPUT PARAMETERS =====
// Multi-Timeframe Setup
htf_timeframe = input.timeframe("60", "Higher Timeframe (Bias)", group="📊 Multi-Timeframe")
ltf_timeframe = input.timeframe("5", "Lower Timeframe (Entry)", group="📊 Multi-Timeframe")

// Trend Analysis
ema9_length = input.int(9, "EMA 9 Length", group="📈 Trend Analysis")
ema21_length = input.int(21, "EMA 21 Length", group="📈 Trend Analysis")

// Market Structure
htf_swing_length = input.int(10, "HTF Swing Length (S&D/S&R)", minval=5, maxval=20, group="🏗️ Market Structure")
ltf_swing_length = input.int(5, "LTF Swing Length (Entry)", minval=3, maxval=10, group="🏗️ Market Structure")

// Support & Resistance / Supply & Demand
sr_lookback = input.int(20, "S&R Lookback Period", minval=10, maxval=50, group="📍 S&D / S&R Zones")
sr_tolerance = input.float(0.1, "S&R Tolerance %", minval=0.05, maxval=0.5, group="📍 S&D / S&R Zones")

// Pullback Analysis
pullback_min = input.float(38.2, "Min Pullback %", minval=20, maxval=50, group="📈 Pullback Analysis")
pullback_max = input.float(61.8, "Max Pullback %", minval=50, maxval=80, group="📈 Pullback Analysis")

// Risk Management
risk_percent = input.float(1.0, "Risk Per Trade %", minval=0.1, maxval=5.0, group="💰 Risk Management")
reward_ratio = input.float(2.0, "Risk:Reward Ratio", minval=1.0, maxval=5.0, group="💰 Risk Management")

// ===== MULTI-TIMEFRAME TREND ANALYSIS =====
// Higher Timeframe (1H) - Market Bias
htf_ema9 = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema9_length))
htf_ema21 = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema21_length))
htf_uptrend = htf_ema9 > htf_ema21
htf_downtrend = htf_ema9 < htf_ema21

// Lower Timeframe (5M) - Entry Confirmation
ltf_ema9 = ta.ema(close, ema9_length)
ltf_ema21 = ta.ema(close, ema21_length)
ltf_uptrend = ltf_ema9 > ltf_ema21
ltf_downtrend = ltf_ema9 < ltf_ema21

// Combined trend (HTF bias + LTF confirmation)
combined_uptrend = htf_uptrend and ltf_uptrend
combined_downtrend = htf_downtrend and ltf_downtrend
trend_aligned = combined_uptrend or combined_downtrend

// Plot EMAs (LTF for entry timing)
plot(ltf_ema9, "EMA 9 (5M)", color=color.blue, linewidth=2)
plot(ltf_ema21, "EMA 21 (5M)", color=color.red, linewidth=2)

// Background color for combined trend
bgcolor(combined_uptrend ? color.new(color.green, 95) :
        combined_downtrend ? color.new(color.red, 95) :
        htf_uptrend ? color.new(color.green, 98) :
        htf_downtrend ? color.new(color.red, 98) : color.new(color.gray, 95))

// ===== MULTI-TIMEFRAME MARKET STRUCTURE =====
// Higher Timeframe (1H) - S&D and S&R Detection
htf_swing_high = request.security(syminfo.tickerid, htf_timeframe, ta.pivothigh(high, htf_swing_length, htf_swing_length))
htf_swing_low = request.security(syminfo.tickerid, htf_timeframe, ta.pivotlow(low, htf_swing_length, htf_swing_length))

// Lower Timeframe (5M) - Entry Structure
ltf_swing_high = ta.pivothigh(high, ltf_swing_length, ltf_swing_length)
ltf_swing_low = ta.pivotlow(low, ltf_swing_length, ltf_swing_length)

// ===== HTF SUPPORT & RESISTANCE / SUPPLY & DEMAND ZONES =====
var array<float> htf_resistance_levels = array.new<float>()
var array<float> htf_support_levels = array.new<float>()
var array<float> htf_supply_zones_top = array.new<float>()
var array<float> htf_supply_zones_bottom = array.new<float>()
var array<float> htf_demand_zones_top = array.new<float>()
var array<float> htf_demand_zones_bottom = array.new<float>()

// Store HTF S&R levels
if not na(htf_swing_high)
    array.push(htf_resistance_levels, htf_swing_high)
    // Create supply zone around the high
    zone_width = htf_swing_high * sr_tolerance / 100
    array.push(htf_supply_zones_top, htf_swing_high + zone_width)
    array.push(htf_supply_zones_bottom, htf_swing_high - zone_width)

    if array.size(htf_resistance_levels) > 10
        array.shift(htf_resistance_levels)
        array.shift(htf_supply_zones_top)
        array.shift(htf_supply_zones_bottom)

if not na(htf_swing_low)
    array.push(htf_support_levels, htf_swing_low)
    // Create demand zone around the low
    zone_width = htf_swing_low * sr_tolerance / 100
    array.push(htf_demand_zones_top, htf_swing_low + zone_width)
    array.push(htf_demand_zones_bottom, htf_swing_low - zone_width)

    if array.size(htf_support_levels) > 10
        array.shift(htf_support_levels)
        array.shift(htf_demand_zones_top)
        array.shift(htf_demand_zones_bottom)

// Check if current price is near HTF S&R or in S&D zones
isNearHTFResistance() =>
    near_resistance = false
    if array.size(htf_resistance_levels) > 0
        for i = 0 to array.size(htf_resistance_levels) - 1
            level = array.get(htf_resistance_levels, i)
            if math.abs(close - level) <= level * sr_tolerance / 100
                near_resistance := true
                break
    near_resistance

isNearHTFSupport() =>
    near_support = false
    if array.size(htf_support_levels) > 0
        for i = 0 to array.size(htf_support_levels) - 1
            level = array.get(htf_support_levels, i)
            if math.abs(close - level) <= level * sr_tolerance / 100
                near_support := true
                break
    near_support

isInHTFSupplyZone() =>
    in_supply = false
    if array.size(htf_supply_zones_top) > 0
        for i = 0 to array.size(htf_supply_zones_top) - 1
            top = array.get(htf_supply_zones_top, i)
            bottom = array.get(htf_supply_zones_bottom, i)
            if close <= top and close >= bottom
                in_supply := true
                break
    in_supply

isInHTFDemandZone() =>
    in_demand = false
    if array.size(htf_demand_zones_top) > 0
        for i = 0 to array.size(htf_demand_zones_top) - 1
            top = array.get(htf_demand_zones_top, i)
            bottom = array.get(htf_demand_zones_bottom, i)
            if close <= top and close >= bottom
                in_demand := true
                break
    in_demand

// HTF Zone Status
near_htf_resistance = isNearHTFResistance()
near_htf_support = isNearHTFSupport()
in_htf_supply_zone = isInHTFSupplyZone()
in_htf_demand_zone = isInHTFDemandZone()

// Arrays for LTF swing points (for entry structure)
var array<float> ltf_highs = array.new<float>()
var array<float> ltf_lows = array.new<float>()

// Store LTF swing points
if not na(ltf_swing_high)
    array.push(ltf_highs, ltf_swing_high)
    if array.size(ltf_highs) > 10
        array.shift(ltf_highs)

if not na(ltf_swing_low)
    array.push(ltf_lows, ltf_swing_low)
    if array.size(ltf_lows) > 10
        array.shift(ltf_lows)

// ===== LTF MARKET STRUCTURE FUNCTIONS =====
isLTFHigherHigh() =>
    if array.size(ltf_highs) >= 2
        latest_high = array.get(ltf_highs, array.size(ltf_highs) - 1)
        previous_high = array.get(ltf_highs, array.size(ltf_highs) - 2)
        latest_high > previous_high
    else
        false

isLTFHigherLow() =>
    if array.size(ltf_lows) >= 2
        latest_low = array.get(ltf_lows, array.size(ltf_lows) - 1)
        previous_low = array.get(ltf_lows, array.size(ltf_lows) - 2)
        latest_low > previous_low
    else
        false

isLTFLowerLow() =>
    if array.size(ltf_lows) >= 2
        latest_low = array.get(ltf_lows, array.size(ltf_lows) - 1)
        previous_low = array.get(ltf_lows, array.size(ltf_lows) - 2)
        latest_low < previous_low
    else
        false

isLTFLowerHigh() =>
    if array.size(ltf_highs) >= 2
        latest_high = array.get(ltf_highs, array.size(ltf_highs) - 1)
        previous_high = array.get(ltf_highs, array.size(ltf_highs) - 2)
        latest_high < previous_high
    else
        false

// ===== LTF MARKET STRUCTURE STATUS =====
var string ltf_market_structure = "NEUTRAL"

// Update LTF market structure during respective trends
if combined_uptrend and not na(ltf_swing_high) and isLTFHigherHigh()
    ltf_market_structure := "BULLISH_HH"

if combined_uptrend and not na(ltf_swing_low) and isLTFHigherLow()
    ltf_market_structure := "BULLISH_HL"

if combined_downtrend and not na(ltf_swing_low) and isLTFLowerLow()
    ltf_market_structure := "BEARISH_LL"

if combined_downtrend and not na(ltf_swing_high) and isLTFLowerHigh()
    ltf_market_structure := "BEARISH_LH"

// ===== VISUAL LABELS =====
// HTF S&D/S&R Zones (plot as lines)
if not na(htf_swing_high) and barstate.isconfirmed
    line.new(bar_index, htf_swing_high, bar_index + 20, htf_swing_high,
             color=color.red, width=2, style=line.style_dashed)
    label.new(bar_index + 10, htf_swing_high, "HTF-R",
             style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)

if not na(htf_swing_low) and barstate.isconfirmed
    line.new(bar_index, htf_swing_low, bar_index + 20, htf_swing_low,
             color=color.green, width=2, style=line.style_dashed)
    label.new(bar_index + 10, htf_swing_low, "HTF-S",
             style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)

// LTF Swing Points (for entry structure)
if not na(ltf_swing_high) and combined_uptrend and barstate.isconfirmed
    label.new(bar_index[ltf_swing_length], ltf_swing_high,
             isLTFHigherHigh() ? "HH" : "H",
             style=label.style_label_down,
             color=isLTFHigherHigh() ? color.green : color.gray,
             textcolor=color.white, size=size.small)

if not na(ltf_swing_low) and combined_uptrend and barstate.isconfirmed
    label.new(bar_index[ltf_swing_length], ltf_swing_low,
             isLTFHigherLow() ? "HL" : "L",
             style=label.style_label_up,
             color=isLTFHigherLow() ? color.green : color.gray,
             textcolor=color.white, size=size.small)

if not na(ltf_swing_high) and combined_downtrend and barstate.isconfirmed
    label.new(bar_index[ltf_swing_length], ltf_swing_high,
             isLTFLowerHigh() ? "LH" : "H",
             style=label.style_label_down,
             color=isLTFLowerHigh() ? color.red : color.gray,
             textcolor=color.white, size=size.small)

if not na(ltf_swing_low) and combined_downtrend and barstate.isconfirmed
    label.new(bar_index[ltf_swing_length], ltf_swing_low,
             isLTFLowerLow() ? "LL" : "L",
             style=label.style_label_up,
             color=isLTFLowerLow() ? color.red : color.gray,
             textcolor=color.white, size=size.small)

// ===== PULLBACK ANALYSIS =====
// Calculate pullback from recent LTF swing
var float pullback_start = na
var float pullback_current = na
var bool valid_pullback = false

// For combined uptrend: pullback from recent high
if combined_uptrend and not na(ltf_swing_high)
    pullback_start := ltf_swing_high

if combined_uptrend and not na(pullback_start)
    pullback_current := (pullback_start - close) / pullback_start * 100
    valid_pullback := pullback_current >= pullback_min and pullback_current <= pullback_max

// For combined downtrend: pullback from recent low
if combined_downtrend and not na(ltf_swing_low)
    pullback_start := ltf_swing_low

if combined_downtrend and not na(pullback_start)
    pullback_current := (close - pullback_start) / pullback_start * 100
    valid_pullback := pullback_current >= pullback_min and pullback_current <= pullback_max

// Reset pullback if trend changes
if ta.change(combined_uptrend) or ta.change(combined_downtrend)
    pullback_start := na
    valid_pullback := false

// ===== MULTI-TIMEFRAME ENTRY CONDITIONS =====
// Long conditions
long_htf_bias = htf_uptrend  // HTF bias bullish
long_ltf_confirmation = ltf_uptrend  // LTF confirmation
long_structure_ok = ltf_market_structure == "BULLISH_HH" or ltf_market_structure == "BULLISH_HL"
long_zone_ok = near_htf_support or in_htf_demand_zone  // HTF S&D/S&R confluence
long_pullback_ok = valid_pullback and close > ltf_ema21  // Valid pullback on LTF
long_entry = long_htf_bias and long_ltf_confirmation and long_structure_ok and long_zone_ok and long_pullback_ok

// Short conditions
short_htf_bias = htf_downtrend  // HTF bias bearish
short_ltf_confirmation = ltf_downtrend  // LTF confirmation
short_structure_ok = ltf_market_structure == "BEARISH_LL" or ltf_market_structure == "BEARISH_LH"
short_zone_ok = near_htf_resistance or in_htf_supply_zone  // HTF S&D/S&R confluence
short_pullback_ok = valid_pullback and close < ltf_ema21  // Valid pullback on LTF
short_entry = short_htf_bias and short_ltf_confirmation and short_structure_ok and short_zone_ok and short_pullback_ok

// ===== RISK MANAGEMENT =====
calculatePositionSize(entryPrice, stopLoss) =>
    riskAmount = strategy.equity * risk_percent / 100
    riskPerShare = math.abs(entryPrice - stopLoss)
    riskAmount / riskPerShare

// ===== TRADE EXECUTION =====
if long_entry and strategy.position_size == 0
    stop_loss = close - ta.atr(14) * 1.5
    take_profit = close + (close - stop_loss) * reward_ratio
    pos_size = calculatePositionSize(close, stop_loss)
    
    strategy.entry("Long", strategy.long, qty=pos_size)
    strategy.exit("Long Exit", from_entry="Long", stop=stop_loss, limit=take_profit)
    
    // Entry label
    if barstate.isconfirmed
        label.new(bar_index, low, 
                 "🟢 LONG\n" + 
                 "Entry: " + str.tostring(close, "#.##") + "\n" + 
                 "SL: " + str.tostring(stop_loss, "#.##") + "\n" + 
                 "TP: " + str.tostring(take_profit, "#.##") + "\n" +
                 "Structure: " + ltf_market_structure,
                 style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)

if short_entry and strategy.position_size == 0
    stop_loss = close + ta.atr(14) * 1.5
    take_profit = close - (stop_loss - close) * reward_ratio
    pos_size = calculatePositionSize(close, stop_loss)
    
    strategy.entry("Short", strategy.short, qty=pos_size)
    strategy.exit("Short Exit", from_entry="Short", stop=stop_loss, limit=take_profit)
    
    // Entry label
    if barstate.isconfirmed
        label.new(bar_index, high, 
                 "🔴 SHORT\n" + 
                 "Entry: " + str.tostring(close, "#.##") + "\n" + 
                 "SL: " + str.tostring(stop_loss, "#.##") + "\n" + 
                 "TP: " + str.tostring(take_profit, "#.##") + "\n" +
                 "Structure: " + market_structure,
                 style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)

// ===== MULTI-TIMEFRAME INFORMATION DISPLAY =====
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 10, bgcolor=color.white, border_width=1)

    table.cell(info_table, 0, 0, "📊 MULTI-TF ANALYSIS", text_color=color.white, bgcolor=color.blue)
    table.cell(info_table, 1, 0, "", text_color=color.white, bgcolor=color.blue)

    table.cell(info_table, 0, 1, "HTF Bias (1H)", text_color=color.black, bgcolor=color.gray)
    htf_trend_text = htf_uptrend ? "🟢 BULLISH" : htf_downtrend ? "🔴 BEARISH" : "⚪ NEUTRAL"
    table.cell(info_table, 1, 1, htf_trend_text, text_color=color.black)

    table.cell(info_table, 0, 2, "LTF Trend (5M)", text_color=color.black, bgcolor=color.gray)
    ltf_trend_text = ltf_uptrend ? "🟢 BULLISH" : ltf_downtrend ? "🔴 BEARISH" : "⚪ NEUTRAL"
    table.cell(info_table, 1, 2, ltf_trend_text, text_color=color.black)

    table.cell(info_table, 0, 3, "Alignment", text_color=color.black, bgcolor=color.gray)
    alignment_text = combined_uptrend ? "🟢 BULLISH" : combined_downtrend ? "🔴 BEARISH" : "❌ NO ALIGN"
    table.cell(info_table, 1, 3, alignment_text, text_color=color.black)

    table.cell(info_table, 0, 4, "HTF Zones", text_color=color.black, bgcolor=color.gray)
    zone_text = in_htf_demand_zone ? "📈 DEMAND" : in_htf_supply_zone ? "📉 SUPPLY" :
                near_htf_support ? "📊 SUPPORT" : near_htf_resistance ? "📊 RESISTANCE" : "🔍 CLEAR"
    table.cell(info_table, 1, 4, zone_text, text_color=color.black)

    table.cell(info_table, 0, 5, "LTF Structure", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 5, ltf_market_structure, text_color=color.black)

    table.cell(info_table, 0, 6, "Pullback", text_color=color.black, bgcolor=color.gray)
    pullback_text = valid_pullback ? "✅ Valid (" + str.tostring(pullback_current, "#.#") + "%)" : "❌ Invalid"
    table.cell(info_table, 1, 6, pullback_text, text_color=color.black)

    table.cell(info_table, 0, 7, "Long Setup", text_color=color.black, bgcolor=color.gray)
    long_status = long_entry ? "🟢 READY" : "⏳ Wait"
    table.cell(info_table, 1, 7, long_status, text_color=color.black)

    table.cell(info_table, 0, 8, "Short Setup", text_color=color.black, bgcolor=color.gray)
    short_status = short_entry ? "🔴 READY" : "⏳ Wait"
    table.cell(info_table, 1, 8, short_status, text_color=color.black)

    table.cell(info_table, 0, 9, "Method", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 9, "HTF Bias + LTF Entry", text_color=color.black)

// Plot entry signals with text
if long_entry and barstate.isconfirmed
    label.new(bar_index, low - ta.atr(14) * 0.5,
             "LONG\nENTRY",
             style=label.style_label_up,
             color=color.lime,
             textcolor=color.black,
             size=size.normal)

if short_entry and barstate.isconfirmed
    label.new(bar_index, high + ta.atr(14) * 0.5,
             "SHORT\nENTRY",
             style=label.style_label_down,
             color=color.red,
             textcolor=color.white,
             size=size.normal)

// Alerts
if long_entry and barstate.isconfirmed
    alert("🟢 LONG SIGNAL - " + syminfo.ticker + " at " + str.tostring(close), alert.freq_once_per_bar)

if short_entry and barstate.isconfirmed
    alert("🔴 SHORT SIGNAL - " + syminfo.ticker + " at " + str.tostring(close), alert.freq_once_per_bar)
