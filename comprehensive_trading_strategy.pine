//@version=5
strategy("Comprehensive Trading Strategy - Fundamental Concepts", overlay=true, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=2)

// ===== INPUT PARAMETERS =====
// Trend Analysis
trendLength = input.int(50, "Trend EMA Length", minval=10, maxval=200, group="Trend Analysis")
htfTimeframe = input.timeframe("1H", "Higher Timeframe for Trend", group="Trend Analysis")
enableHTFConfirmation = input.bool(true, "Enable HTF Trend Confirmation", group="Trend Analysis")

// Market Structure
swingLength = input.int(10, "Swing Points Length", minval=5, maxval=50, group="Market Structure")
structureBreakConfirm = input.int(3, "Structure Break Confirmation Bars", minval=1, maxval=10, group="Market Structure")

// Support & Resistance
srLookback = input.int(20, "S&R Lookback Period", minval=10, maxval=100, group="Support & Resistance")
srTolerance = input.float(0.1, "S&R Tolerance %", minval=0.01, maxval=1.0, group="Support & Resistance")

// Supply & Demand Zones
sdZoneStrength = input.int(3, "S&D Zone Min Touches", minval=2, maxval=10, group="Supply & Demand")
sdZoneWidth = input.float(0.2, "S&D Zone Width %", minval=0.05, maxval=1.0, group="Supply & Demand")

// Pullback Validation
pullbackDepth = input.float(38.2, "Min Pullback Depth %", minval=23.6, maxval=61.8, group="Pullback Analysis")
pullbackMaxDepth = input.float(61.8, "Max Pullback Depth %", minval=50.0, maxval=78.6, group="Pullback Analysis")

// Price Action
enableCandlestickPatterns = input.bool(true, "Enable Candlestick Patterns", group="Price Action")
volumeConfirmation = input.bool(true, "Require Volume Confirmation", group="Price Action")
minVolumeMultiplier = input.float(1.2, "Min Volume Multiplier", minval=1.0, maxval=3.0, group="Price Action")

// Risk Management
riskPercent = input.float(1.0, "Risk Per Trade %", minval=0.1, maxval=5.0, group="Risk Management")
rewardRatio = input.float(2.0, "Risk:Reward Ratio", minval=1.0, maxval=5.0, group="Risk Management")

// ===== ADVANCED TREND ANALYSIS SYSTEM =====
// Multiple EMA trend confirmation
fastEMA = ta.ema(close, 12)
mediumEMA = ta.ema(close, 26)
slowEMA = ta.ema(close, trendLength)

// EMA alignment for trend strength
emaAlignment = fastEMA > mediumEMA and mediumEMA > slowEMA ? 1 :
               fastEMA < mediumEMA and mediumEMA < slowEMA ? -1 : 0

// ADX for trend strength measurement
[diPlus, diMinus, adx] = ta.dmi(14, 14)
trendStrength = adx > 25 ? "STRONG" : adx > 20 ? "MODERATE" : "WEAK"
trendDirection = diPlus > diMinus ? 1 : -1

// MACD trend confirmation
[macdLine, signalLine, histLine] = ta.macd(close, 12, 26, 9)
macdTrend = macdLine > signalLine ? 1 : -1

// Current timeframe trend (multiple confirmations)
currentTrend = close > slowEMA ? 1 : close < slowEMA ? -1 : 0
trendConfirmation = emaAlignment == currentTrend and trendDirection == currentTrend and macdTrend == currentTrend

// Higher timeframe trend confirmation
htfFastEMA = request.security(syminfo.tickerid, htfTimeframe, ta.ema(close, 12))
htfSlowEMA = request.security(syminfo.tickerid, htfTimeframe, ta.ema(close, trendLength))
htfTrend = htfFastEMA > htfSlowEMA ? 1 : htfFastEMA < htfSlowEMA ? -1 : 0

// Combined trend direction with strength filter
isBullishTrend = currentTrend == 1 and trendConfirmation and adx > 20 and (not enableHTFConfirmation or htfTrend == 1)
isBearishTrend = currentTrend == -1 and trendConfirmation and adx > 20 and (not enableHTFConfirmation or htfTrend == -1)

// Trend change detection
var bool trendChanged = false
if (isBullishTrend and not isBullishTrend[1]) or (isBearishTrend and not isBearishTrend[1])
    trendChanged := true
else
    trendChanged := false

// ===== ADVANCED MARKET STRUCTURE ANALYSIS =====
// Enhanced Swing High/Low Detection with multiple timeframes
swingHigh = ta.pivothigh(high, swingLength, swingLength)
swingLow = ta.pivotlow(low, swingLength, swingLength)

// Store swing points with timestamps and strength
var array<float> swingHighs = array.new<float>()
var array<float> swingLows = array.new<float>()
var array<int> swingHighBars = array.new<int>()
var array<int> swingLowBars = array.new<int>()
var array<float> swingHighStrength = array.new<float>()
var array<float> swingLowStrength = array.new<float>()

// Calculate swing strength based on volume and range
calculateSwingStrength(swingPrice, isHigh) =>
    strength = 0.0
    for i = 1 to swingLength
        if isHigh
            if high[i] < swingPrice
                strength := strength + volume[i] * (high[i] / swingPrice)
        else
            if low[i] > swingPrice
                strength := strength + volume[i] * (swingPrice / low[i])
    strength

if not na(swingHigh)
    strength = calculateSwingStrength(swingHigh, true)
    array.push(swingHighs, swingHigh)
    array.push(swingHighBars, bar_index - swingLength)
    array.push(swingHighStrength, strength)
    if array.size(swingHighs) > 15
        array.shift(swingHighs)
        array.shift(swingHighBars)
        array.shift(swingHighStrength)

if not na(swingLow)
    strength = calculateSwingStrength(swingLow, false)
    array.push(swingLows, swingLow)
    array.push(swingLowBars, bar_index - swingLength)
    array.push(swingLowStrength, strength)
    if array.size(swingLows) > 15
        array.shift(swingLows)
        array.shift(swingLowBars)
        array.shift(swingLowStrength)

// Advanced Market Structure Analysis
getAdvancedMarketStructure() =>
    if array.size(swingHighs) >= 3 and array.size(swingLows) >= 3
        // Get last 3 highs and lows
        h1 = array.get(swingHighs, array.size(swingHighs) - 1)
        h2 = array.get(swingHighs, array.size(swingHighs) - 2)
        h3 = array.get(swingHighs, array.size(swingHighs) - 3)

        l1 = array.get(swingLows, array.size(swingLows) - 1)
        l2 = array.get(swingLows, array.size(swingLows) - 2)
        l3 = array.get(swingLows, array.size(swingLows) - 3)

        // Analyze pattern
        higherHighs = h1 > h2 and h2 > h3
        higherLows = l1 > l2 and l2 > l3
        lowerHighs = h1 < h2 and h2 < h3
        lowerLows = l1 < l2 and l2 < l3

        // Determine structure
        if higherHighs and higherLows
            "STRONG_BULLISH"
        else if lowerHighs and lowerLows
            "STRONG_BEARISH"
        else if higherHighs and not lowerLows
            "BULLISH_STRUCTURE"
        else if lowerLows and not higherHighs
            "BEARISH_STRUCTURE"
        else if h1 > h2 and h1 > h3 and l1 < l2 and l1 < l3
            "EXPANDING_RANGE"
        else
            "CONSOLIDATION"
    else
        "INSUFFICIENT_DATA"

marketStructure = getAdvancedMarketStructure()

// Enhanced Break of Structure Detection with Change of Character (CHoCH)
var bool bosDetected = false
var bool chochDetected = false
var string bosDirection = ""
var string chochDirection = ""
var float lastBosLevel = na
var float lastChochLevel = na

if array.size(swingHighs) >= 2 and array.size(swingLows) >= 2
    lastHigh = array.get(swingHighs, array.size(swingHighs) - 1)
    prevHigh = array.get(swingHighs, array.size(swingHighs) - 2)
    lastLow = array.get(swingLows, array.size(swingLows) - 1)
    prevLow = array.get(swingLows, array.size(swingLows) - 2)

    // Bullish BOS - break above recent high with confirmation
    if close > lastHigh and close[structureBreakConfirm] <= lastHigh and volume > ta.sma(volume, 20) * 1.2
        bosDetected := true
        bosDirection := "BULLISH"
        lastBosLevel := lastHigh

    // Bearish BOS - break below recent low with confirmation
    if close < lastLow and close[structureBreakConfirm] >= lastLow and volume > ta.sma(volume, 20) * 1.2
        bosDetected := true
        bosDirection := "BEARISH"
        lastBosLevel := lastLow

    // Change of Character Detection (trend reversal)
    if marketStructure == "STRONG_BULLISH" and close < prevLow
        chochDetected := true
        chochDirection := "BEARISH"
        lastChochLevel := prevLow

    if marketStructure == "STRONG_BEARISH" and close > prevHigh
        chochDetected := true
        chochDirection := "BULLISH"
        lastChochLevel := prevHigh

// Market Structure Score (0-100)
getMarketStructureScore() =>
    score = 50  // Neutral base

    if marketStructure == "STRONG_BULLISH"
        score := 85
    else if marketStructure == "BULLISH_STRUCTURE"
        score := 70
    else if marketStructure == "STRONG_BEARISH"
        score := 15
    else if marketStructure == "BEARISH_STRUCTURE"
        score := 30
    else if marketStructure == "CONSOLIDATION"
        score := 50

    // Adjust for recent BOS
    if bosDetected
        if bosDirection == "BULLISH"
            score := math.min(95, score + 15)
        else
            score := math.max(5, score - 15)

    // Adjust for CHoCH
    if chochDetected
        if chochDirection == "BULLISH"
            score := math.min(90, score + 10)
        else
            score := math.max(10, score - 10)

    score

structureScore = getMarketStructureScore()

// ===== SUPPORT & RESISTANCE SYSTEM =====
// Dynamic S&R Level Detection
var array<float> supportLevels = array.new<float>()
var array<float> resistanceLevels = array.new<float>()

// Function to check if price level is significant
isSignificantLevel(price, tolerance) =>
    touchCount = 0
    for i = 1 to srLookback
        if math.abs(high[i] - price) <= price * tolerance / 100 or math.abs(low[i] - price) <= price * tolerance / 100
            touchCount := touchCount + 1
    touchCount >= 3

// Update S&R levels
if bar_index % 10 == 0  // Update every 10 bars for performance
    array.clear(supportLevels)
    array.clear(resistanceLevels)
    
    for i = 1 to srLookback
        testPrice = low[i]
        if isSignificantLevel(testPrice, srTolerance)
            array.push(supportLevels, testPrice)
        
        testPrice := high[i]
        if isSignificantLevel(testPrice, srTolerance)
            array.push(resistanceLevels, testPrice)

// Check if current price is near S&R
nearSupport = false
nearResistance = false

if array.size(supportLevels) > 0
    for i = 0 to array.size(supportLevels) - 1
        level = array.get(supportLevels, i)
        if math.abs(close - level) <= close * srTolerance / 100
            nearSupport := true

if array.size(resistanceLevels) > 0
    for i = 0 to array.size(resistanceLevels) - 1
        level = array.get(resistanceLevels, i)
        if math.abs(close - level) <= close * srTolerance / 100
            nearResistance := true

// ===== ADVANCED SUPPLY & DEMAND ZONES =====
// Enhanced S&D Zone Detection with institutional behavior analysis
type SupplyDemandZone
    float top
    float bottom
    int strength
    int touches
    bool active
    string zoneType
    float volume
    int creationBar

var array<SupplyDemandZone> demandZones = array.new<SupplyDemandZone>()
var array<SupplyDemandZone> supplyZones = array.new<SupplyDemandZone>()

// Detect institutional accumulation/distribution patterns
detectInstitutionalActivity() =>
    // Look for consolidation followed by strong moves
    consolidationBars = 10
    avgRange = ta.atr(20)
    avgVol = ta.sma(volume, 20)

    // Check for tight consolidation
    isConsolidating = true
    consolidationHigh = high
    consolidationLow = low
    consolidationVolume = 0.0

    for i = 1 to consolidationBars
        if high[i] - low[i] > avgRange * 0.7
            isConsolidating := false
        consolidationHigh := math.max(consolidationHigh, high[i])
        consolidationLow := math.min(consolidationLow, low[i])
        consolidationVolume := consolidationVolume + volume[i]

    consolidationRange = consolidationHigh - consolidationLow

    // Detect breakout with volume
    bullishBreakout = close > consolidationHigh and volume > avgVol * minVolumeMultiplier and isConsolidating
    bearishBreakout = close < consolidationLow and volume > avgVol * minVolumeMultiplier and isConsolidating

    [bullishBreakout, bearishBreakout, consolidationLow, consolidationHigh, consolidationVolume]

[bullishBO, bearishBO, consLow, consHigh, consVol] = detectInstitutionalActivity()

// Create demand zones from bullish breakouts
if bullishBO
    newZone = SupplyDemandZone.new(
         top = consLow + (consHigh - consLow) * sdZoneWidth / 100,
         bottom = consLow,
         strength = math.round(consVol / ta.sma(volume, 50)),
         touches = 0,
         active = true,
         zoneType = "DEMAND",
         volume = consVol,
         creationBar = bar_index
     )
    array.push(demandZones, newZone)
    if array.size(demandZones) > 10
        array.shift(demandZones)

// Create supply zones from bearish breakouts
if bearishBO
    newZone = SupplyDemandZone.new(
         top = consHigh,
         bottom = consHigh - (consHigh - consLow) * sdZoneWidth / 100,
         strength = math.round(consVol / ta.sma(volume, 50)),
         touches = 0,
         active = true,
         zoneType = "SUPPLY",
         volume = consVol,
         creationBar = bar_index
     )
    array.push(supplyZones, newZone)
    if array.size(supplyZones) > 10
        array.shift(supplyZones)

// Update zone touches and activity
updateZones(zones) =>
    if array.size(zones) > 0
        for i = 0 to array.size(zones) - 1
            zone = array.get(zones, i)

            // Check if price touched the zone
            if zone.zoneType == "DEMAND"
                if low <= zone.top and high >= zone.bottom
                    zone.touches := zone.touches + 1
            else  // SUPPLY
                if high >= zone.bottom and low <= zone.top
                    zone.touches := zone.touches + 1

            // Deactivate zone after too many touches or if broken significantly
            if zone.touches >= sdZoneStrength
                zone.active := false

            // Deactivate old zones (older than 100 bars)
            if bar_index - zone.creationBar > 100
                zone.active := false

updateZones(demandZones)
updateZones(supplyZones)

// Check current price position relative to zones
getCurrentZoneStatus() =>
    inDemand = false
    inSupply = false
    demandStrength = 0
    supplyStrength = 0

    // Check demand zones
    if array.size(demandZones) > 0
        for i = 0 to array.size(demandZones) - 1
            zone = array.get(demandZones, i)
            if zone.active and close >= zone.bottom and close <= zone.top
                inDemand := true
                demandStrength := math.max(demandStrength, zone.strength)

    // Check supply zones
    if array.size(supplyZones) > 0
        for i = 0 to array.size(supplyZones) - 1
            zone = array.get(supplyZones, i)
            if zone.active and close >= zone.bottom and close <= zone.top
                inSupply := true
                supplyStrength := math.max(supplyStrength, zone.strength)

    [inDemand, inSupply, demandStrength, supplyStrength]

[inDemandZone, inSupplyZone, demandZoneStrength, supplyZoneStrength] = getCurrentZoneStatus()

// Zone reaction probability based on strength and touches
getDemandZoneReactionProb() =>
    if inDemandZone and array.size(demandZones) > 0
        totalProb = 0.0
        activeZones = 0

        for i = 0 to array.size(demandZones) - 1
            zone = array.get(demandZones, i)
            if zone.active and close >= zone.bottom and close <= zone.top
                // Higher strength = higher probability
                strengthFactor = math.min(zone.strength / 3.0, 1.0)
                // Fewer touches = higher probability
                touchFactor = math.max(0.2, 1.0 - zone.touches / sdZoneStrength)
                // Recent zones more reliable
                ageFactor = math.max(0.3, 1.0 - (bar_index - zone.creationBar) / 100.0)

                zoneProb = strengthFactor * touchFactor * ageFactor
                totalProb := totalProb + zoneProb
                activeZones := activeZones + 1

        activeZones > 0 ? totalProb / activeZones : 0.0
    else
        0.0

getSupplyZoneReactionProb() =>
    if inSupplyZone and array.size(supplyZones) > 0
        totalProb = 0.0
        activeZones = 0

        for i = 0 to array.size(supplyZones) - 1
            zone = array.get(supplyZones, i)
            if zone.active and close >= zone.bottom and close <= zone.top
                strengthFactor = math.min(zone.strength / 3.0, 1.0)
                touchFactor = math.max(0.2, 1.0 - zone.touches / sdZoneStrength)
                ageFactor = math.max(0.3, 1.0 - (bar_index - zone.creationBar) / 100.0)

                zoneProb = strengthFactor * touchFactor * ageFactor
                totalProb := totalProb + zoneProb
                activeZones := activeZones + 1

        activeZones > 0 ? totalProb / activeZones : 0.0
    else
        0.0

demandReactionProb = getDemandZoneReactionProb()
supplyReactionProb = getSupplyZoneReactionProb()

// ===== PULLBACK VALIDATION =====
// Fibonacci-based pullback analysis
var float trendStart = na
var float trendEnd = na
var bool validPullback = false

// Detect trend start and current level for pullback calculation
if isBullishTrend and not isBullishTrend[1]
    trendStart := low
    trendEnd := high
else if isBullishTrend
    trendEnd := math.max(trendEnd, high)

if isBearishTrend and not isBearishTrend[1]
    trendStart := high
    trendEnd := low
else if isBearishTrend
    trendEnd := math.min(trendEnd, low)

// Calculate pullback percentage
if not na(trendStart) and not na(trendEnd)
    if isBullishTrend
        pullbackPct = (trendEnd - close) / (trendEnd - trendStart) * 100
        validPullback := pullbackPct >= pullbackDepth and pullbackPct <= pullbackMaxDepth
    else if isBearishTrend
        pullbackPct = (close - trendEnd) / (trendStart - trendEnd) * 100
        validPullback := pullbackPct >= pullbackDepth and pullbackPct <= pullbackMaxDepth

// ===== PRICE ACTION ANALYSIS =====
// Volume confirmation
avgVolume = ta.sma(volume, 20)
volumeConfirmed = not volumeConfirmation or volume > avgVolume * minVolumeMultiplier

// Basic candlestick patterns
isDoji = math.abs(close - open) <= (high - low) * 0.1
isHammer = (close > open ? close - open : open - close) <= (high - low) * 0.3 and (low < open and low < close) and (high - math.max(close, open)) <= (high - low) * 0.1
isEngulfing = (close > open and close[1] < open[1] and close > open[1] and open < close[1]) or (close < open and close[1] > open[1] and close < open[1] and open > close[1])

bullishPriceAction = (isHammer and close > open) or (isEngulfing and close > open)
bearishPriceAction = (isHammer and close < open) or (isEngulfing and close < open)

// ===== ADVANCED ENTRY CONDITIONS WITH WEIGHTED SCORING =====
// Weighted scoring system for more nuanced entry decisions
getAdvancedEntryScore(isLong) =>
    score = 0.0
    maxScore = 100.0

    // 1. Trend Alignment (Weight: 25%)
    if isLong
        if isBullishTrend and trendStrength == "STRONG"
            score := score + 25
        else if isBullishTrend and trendStrength == "MODERATE"
            score := score + 15
        else if isBullishTrend
            score := score + 10
    else
        if isBearishTrend and trendStrength == "STRONG"
            score := score + 25
        else if isBearishTrend and trendStrength == "MODERATE"
            score := score + 15
        else if isBearishTrend
            score := score + 10

    // 2. Market Structure (Weight: 20%)
    structureWeight = structureScore / 100 * 20
    if isLong and structureScore > 60
        score := score + structureWeight
    else if not isLong and structureScore < 40
        score := score + (100 - structureScore) / 100 * 20

    // 3. Supply/Demand Zone Quality (Weight: 20%)
    if isLong and inDemandZone
        score := score + demandReactionProb * 20
    else if not isLong and inSupplyZone
        score := score + supplyReactionProb * 20

    // 4. Support/Resistance Confluence (Weight: 15%)
    if isLong and nearSupport
        score := score + 15
    else if not isLong and nearResistance
        score := score + 15

    // 5. Pullback Quality (Weight: 10%)
    if validPullback
        score := score + 10

    // 6. Price Action Confirmation (Weight: 10%)
    if isLong and bullishPriceAction and volumeConfirmed
        score := score + 10
    else if not isLong and bearishPriceAction and volumeConfirmed
        score := score + 10
    else if volumeConfirmed
        score := score + 5

    score

// Calculate advanced scores
longAdvancedScore = getAdvancedEntryScore(true)
shortAdvancedScore = getAdvancedEntryScore(false)

// Traditional binary conditions for comparison
longConditions = array.new<bool>()
array.push(longConditions, isBullishTrend)
array.push(longConditions, structureScore > 60)
array.push(longConditions, nearSupport or (inDemandZone and demandReactionProb > 0.3))
array.push(longConditions, validPullback)
array.push(longConditions, bullishPriceAction or not enableCandlestickPatterns)
array.push(longConditions, volumeConfirmed)

shortConditions = array.new<bool>()
array.push(shortConditions, isBearishTrend)
array.push(shortConditions, structureScore < 40)
array.push(shortConditions, nearResistance or (inSupplyZone and supplyReactionProb > 0.3))
array.push(shortConditions, validPullback)
array.push(shortConditions, bearishPriceAction or not enableCandlestickPatterns)
array.push(shortConditions, volumeConfirmed)

countTrue(conditions) =>
    count = 0
    for i = 0 to array.size(conditions) - 1
        if array.get(conditions, i)
            count := count + 1
    count

longScore = countTrue(longConditions)
shortScore = countTrue(shortConditions)

// Multi-tier entry system
// Tier 1: High confidence (Advanced Score >= 70)
// Tier 2: Medium confidence (Advanced Score >= 50 AND Binary Score >= 4)
// Tier 3: Low confidence (Binary Score >= 5)

longEntryTier1 = longAdvancedScore >= 70
longEntryTier2 = longAdvancedScore >= 50 and longScore >= 4
longEntryTier3 = longScore >= 5

shortEntryTier1 = shortAdvancedScore >= 70
shortEntryTier2 = shortAdvancedScore >= 50 and shortScore >= 4
shortEntryTier3 = shortScore >= 5

// Final entry signals (prioritize higher tiers)
longEntry = longEntryTier1 or longEntryTier2
shortEntry = shortEntryTier1 or shortEntryTier2

// Entry confidence level
longConfidence = longEntryTier1 ? "HIGH" : longEntryTier2 ? "MEDIUM" : longEntryTier3 ? "LOW" : "NONE"
shortConfidence = shortEntryTier1 ? "HIGH" : shortEntryTier2 ? "MEDIUM" : shortEntryTier3 ? "LOW" : "NONE"

// ===== ADAPTIVE RISK MANAGEMENT =====
// Dynamic position sizing based on confidence and market conditions
getAdaptivePositionSize(entryPrice, stopLoss, confidence, isLong) =>
    baseRisk = strategy.equity * riskPercent / 100
    riskPerShare = math.abs(entryPrice - stopLoss)
    baseSize = baseRisk / riskPerShare

    // Confidence multiplier
    confidenceMultiplier = confidence == "HIGH" ? 1.5 : confidence == "MEDIUM" ? 1.0 : 0.5

    // Market condition multiplier
    marketMultiplier = 1.0
    if isLong
        if trendStrength == "STRONG" and structureScore > 70
            marketMultiplier := 1.2
        else if trendStrength == "WEAK" or structureScore < 60
            marketMultiplier := 0.8
    else
        if trendStrength == "STRONG" and structureScore < 30
            marketMultiplier := 1.2
        else if trendStrength == "WEAK" or structureScore > 40
            marketMultiplier := 0.8

    // Zone quality multiplier
    zoneMultiplier = 1.0
    if isLong and inDemandZone
        zoneMultiplier := 1.0 + (demandReactionProb * 0.3)
    else if not isLong and inSupplyZone
        zoneMultiplier := 1.0 + (supplyReactionProb * 0.3)

    finalSize = baseSize * confidenceMultiplier * marketMultiplier * zoneMultiplier
    math.min(finalSize, strategy.equity * 0.1)  // Max 10% of equity per trade

// Dynamic stop loss calculation
getAdaptiveStopLoss(entryPrice, isLong, confidence) =>
    baseATR = ta.atr(14)

    // Confidence-based ATR multiplier
    atrMultiplier = confidence == "HIGH" ? 1.5 : confidence == "MEDIUM" ? 2.0 : 2.5

    // Market volatility adjustment
    volatilityAdj = adx > 30 ? 1.2 : adx < 20 ? 0.8 : 1.0

    finalATR = baseATR * atrMultiplier * volatilityAdj

    if isLong
        entryPrice - finalATR
    else
        entryPrice + finalATR

// Dynamic take profit calculation
getAdaptiveTakeProfit(entryPrice, stopLoss, isLong, confidence) =>
    baseRR = rewardRatio

    // Adjust RR based on confidence and market structure
    rrMultiplier = 1.0
    if confidence == "HIGH"
        rrMultiplier := 1.3
    else if confidence == "LOW"
        rrMultiplier := 0.8

    // Structure-based adjustment
    if isLong and structureScore > 80
        rrMultiplier := rrMultiplier * 1.2
    else if not isLong and structureScore < 20
        rrMultiplier := rrMultiplier * 1.2

    finalRR = baseRR * rrMultiplier
    riskAmount = math.abs(entryPrice - stopLoss)

    if isLong
        entryPrice + (riskAmount * finalRR)
    else
        entryPrice - (riskAmount * finalRR)

// ===== ENHANCED TRADE EXECUTION =====
// Long trades
if longEntry and strategy.position_size == 0
    adaptiveStopLoss = getAdaptiveStopLoss(close, true, longConfidence)
    adaptiveTakeProfit = getAdaptiveTakeProfit(close, adaptiveStopLoss, true, longConfidence)
    adaptivePositionSize = getAdaptivePositionSize(close, adaptiveStopLoss, longConfidence, true)

    strategy.entry("Long", strategy.long, qty=adaptivePositionSize)
    strategy.exit("Long Exit", from_entry="Long", stop=adaptiveStopLoss, limit=adaptiveTakeProfit)

    // Store trade info for analysis
    if barstate.isconfirmed
        label.new(bar_index, low,
                 "🟢 LONG\n" +
                 "Conf: " + longConfidence + "\n" +
                 "Score: " + str.tostring(longAdvancedScore, "#.#") + "\n" +
                 "Size: " + str.tostring(adaptivePositionSize * close / strategy.equity * 100, "#.#") + "%\n" +
                 "SL: " + str.tostring(adaptiveStopLoss, "#.##") + "\n" +
                 "TP: " + str.tostring(adaptiveTakeProfit, "#.##"),
                 style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)

// Short trades
if shortEntry and strategy.position_size == 0
    adaptiveStopLoss = getAdaptiveStopLoss(close, false, shortConfidence)
    adaptiveTakeProfit = getAdaptiveTakeProfit(close, adaptiveStopLoss, false, shortConfidence)
    adaptivePositionSize = getAdaptivePositionSize(close, adaptiveStopLoss, shortConfidence, false)

    strategy.entry("Short", strategy.short, qty=adaptivePositionSize)
    strategy.exit("Short Exit", from_entry="Short", stop=adaptiveStopLoss, limit=adaptiveTakeProfit)

    // Store trade info for analysis
    if barstate.isconfirmed
        label.new(bar_index, high,
                 "🔴 SHORT\n" +
                 "Conf: " + shortConfidence + "\n" +
                 "Score: " + str.tostring(shortAdvancedScore, "#.#") + "\n" +
                 "Size: " + str.tostring(adaptivePositionSize * close / strategy.equity * 100, "#.#") + "%\n" +
                 "SL: " + str.tostring(adaptiveStopLoss, "#.##") + "\n" +
                 "TP: " + str.tostring(adaptiveTakeProfit, "#.##"),
                 style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)

// ===== VISUALIZATION =====
// Plot trend EMA
plot(trendEMA, "Trend EMA", color=isBullishTrend ? color.green : isBearishTrend ? color.red : color.gray, linewidth=2)

// Plot entry signals
plotshape(longEntry, "Long Entry", shape.triangleup, location.belowbar, color.green, size=size.normal)
plotshape(shortEntry, "Short Entry", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Background color for market structure
bgcolor(marketStructure == "BULLISH_STRUCTURE" ? color.new(color.green, 95) : marketStructure == "BEARISH_STRUCTURE" ? color.new(color.red, 95) : na)

// Enhanced information display
if barstate.islast
    var table infoTable = table.new(position.top_right, 2, 12, bgcolor=color.white, border_width=1)

    // Header
    table.cell(infoTable, 0, 0, "📊 MARKET ANALYSIS", text_color=color.white, bgcolor=color.blue, text_size=size.normal)
    table.cell(infoTable, 1, 0, "", text_color=color.white, bgcolor=color.blue)

    // Trend Analysis
    table.cell(infoTable, 0, 1, "Trend", text_color=color.black, bgcolor=color.gray)
    trendText = isBullishTrend ? "🟢 BULLISH" : isBearishTrend ? "🔴 BEARISH" : "⚪ NEUTRAL"
    table.cell(infoTable, 1, 1, trendText + " (" + trendStrength + ")", text_color=color.black)

    // Market Structure
    table.cell(infoTable, 0, 2, "Structure", text_color=color.black, bgcolor=color.gray)
    structureText = marketStructure + " (" + str.tostring(structureScore) + "/100)"
    table.cell(infoTable, 1, 2, structureText, text_color=color.black)

    // BOS/CHoCH Status
    table.cell(infoTable, 0, 3, "BOS/CHoCH", text_color=color.black, bgcolor=color.gray)
    bosText = bosDetected ? "BOS: " + bosDirection : chochDetected ? "CHoCH: " + chochDirection : "None"
    table.cell(infoTable, 1, 3, bosText, text_color=color.black)

    // Supply/Demand Zones
    table.cell(infoTable, 0, 4, "S&D Zones", text_color=color.black, bgcolor=color.gray)
    sdText = inDemandZone ? "📈 Demand (" + str.tostring(demandReactionProb * 100, "#") + "%)" :
             inSupplyZone ? "📉 Supply (" + str.tostring(supplyReactionProb * 100, "#") + "%)" : "None"
    table.cell(infoTable, 1, 4, sdText, text_color=color.black)

    // Support/Resistance
    table.cell(infoTable, 0, 5, "S&R", text_color=color.black, bgcolor=color.gray)
    srText = nearSupport ? "📊 Near Support" : nearResistance ? "📊 Near Resistance" : "Clear"
    table.cell(infoTable, 1, 5, srText, text_color=color.black)

    // Pullback Status
    table.cell(infoTable, 0, 6, "Pullback", text_color=color.black, bgcolor=color.gray)
    pullbackText = validPullback ? "✅ Valid" : "❌ Invalid"
    table.cell(infoTable, 1, 6, pullbackText, text_color=color.black)

    // Long Analysis
    table.cell(infoTable, 0, 7, "🟢 LONG", text_color=color.white, bgcolor=color.green)
    table.cell(infoTable, 1, 7, "", text_color=color.white, bgcolor=color.green)

    table.cell(infoTable, 0, 8, "Score", text_color=color.black, bgcolor=color.gray)
    table.cell(infoTable, 1, 8, str.tostring(longAdvancedScore, "#.#") + "/100 (" + longConfidence + ")", text_color=color.black)

    table.cell(infoTable, 0, 9, "Binary", text_color=color.black, bgcolor=color.gray)
    table.cell(infoTable, 1, 9, str.tostring(longScore) + "/6", text_color=color.black)

    // Short Analysis
    table.cell(infoTable, 0, 10, "🔴 SHORT", text_color=color.white, bgcolor=color.red)
    table.cell(infoTable, 1, 10, "", text_color=color.white, bgcolor=color.red)

    table.cell(infoTable, 0, 11, "Score", text_color=color.black, bgcolor=color.gray)
    table.cell(infoTable, 1, 11, str.tostring(shortAdvancedScore, "#.#") + "/100 (" + shortConfidence + ")", text_color=color.black)

    table.cell(infoTable, 0, 12, "Binary", text_color=color.black, bgcolor=color.gray)
    table.cell(infoTable, 1, 12, str.tostring(shortScore) + "/6", text_color=color.black)
